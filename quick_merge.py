#!/usr/bin/env python3
"""
快速合并当前目录下所有.cap文件的简化脚本
"""

import os
import glob
from datetime import datetime

try:
    from scapy.all import PcapReader, PcapWriter
except ImportError:
    print("错误: 需要安装scapy库")
    print("请运行: pip install scapy")
    exit(1)


def quick_merge_caps():
    """快速合并当前目录下的所有.cap文件"""
    
    # 获取所有.cap文件
    cap_files = glob.glob("*.cap")
    cap_files.sort()  # 按文件名排序
    
    if not cap_files:
        print("当前目录下没有找到.cap文件")
        return
    
    print(f"找到 {len(cap_files)} 个.cap文件:")
    for i, file in enumerate(cap_files, 1):
        print(f"  {i}. {file}")
    
    # 生成输出文件名
    output_file = f"merged_all_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pcap"
    
    print(f"\n开始合并到文件: {output_file}")
    
    total_packets = 0
    
    try:
        with PcapWriter(output_file, append=False, sync=True) as writer:
            for i, cap_file in enumerate(cap_files, 1):
                print(f"正在处理 ({i}/{len(cap_files)}): {cap_file}")
                
                try:
                    with PcapReader(cap_file) as reader:
                        packet_count = 0
                        for packet in reader:
                            writer.write(packet)
                            packet_count += 1
                        
                        total_packets += packet_count
                        print(f"  -> 添加了 {packet_count} 个数据包")
                        
                except Exception as e:
                    print(f"  -> 警告: 处理文件时出错: {e}")
                    continue
        
        print(f"\n✅ 合并完成!")
        print(f"📊 总数据包: {total_packets}")
        print(f"📁 输出文件: {output_file}")
        print(f"📏 文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
        
    except Exception as e:
        print(f"❌ 合并失败: {e}")


if __name__ == "__main__":
    quick_merge_caps()
