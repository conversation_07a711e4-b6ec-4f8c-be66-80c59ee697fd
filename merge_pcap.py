#!/usr/bin/env python3
"""
抓包文件合并工具
支持合并多个.cap/.pcap文件为一个文件
"""

import os
import glob
import argparse
from datetime import datetime
import sys

try:
    from scapy.all import rdpcap, wrpcap, PcapReader, PcapWriter
    from scapy.packet import Packet
except ImportError:
    print("错误: 需要安装scapy库")
    print("请运行: pip install scapy")
    sys.exit(1)


def get_pcap_files(directory="."):
    """获取目录中的所有抓包文件"""
    patterns = ["*.cap", "*.pcap", "*.pcapng"]
    files = []
    
    for pattern in patterns:
        files.extend(glob.glob(os.path.join(directory, pattern)))
    
    # 按文件名排序，确保按时间顺序合并
    files.sort()
    return files


def merge_pcap_files(input_files, output_file, verbose=False):
    """合并多个抓包文件"""
    if not input_files:
        print("错误: 没有找到抓包文件")
        return False
    
    print(f"找到 {len(input_files)} 个抓包文件")
    
    if verbose:
        for i, file in enumerate(input_files, 1):
            print(f"  {i}. {os.path.basename(file)}")
    
    total_packets = 0
    
    try:
        # 创建输出文件写入器
        with PcapWriter(output_file, append=False, sync=True) as writer:
            
            for i, input_file in enumerate(input_files, 1):
                if verbose:
                    print(f"正在处理文件 {i}/{len(input_files)}: {os.path.basename(input_file)}")
                
                try:
                    # 读取并写入数据包
                    with PcapReader(input_file) as reader:
                        packet_count = 0
                        for packet in reader:
                            writer.write(packet)
                            packet_count += 1
                            total_packets += 1
                        
                        if verbose:
                            print(f"  -> 处理了 {packet_count} 个数据包")
                            
                except Exception as e:
                    print(f"警告: 处理文件 {input_file} 时出错: {e}")
                    continue
        
        print(f"合并完成!")
        print(f"总共合并了 {total_packets} 个数据包")
        print(f"输出文件: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"错误: 合并过程中出现问题: {e}")
        return False


def merge_pcap_memory_efficient(input_files, output_file, verbose=False):
    """内存高效的合并方式，适用于大文件"""
    if not input_files:
        print("错误: 没有找到抓包文件")
        return False
    
    print(f"使用内存高效模式合并 {len(input_files)} 个文件")
    
    total_packets = 0
    
    try:
        writer = PcapWriter(output_file, append=False, sync=True)
        
        for i, input_file in enumerate(input_files, 1):
            if verbose:
                print(f"正在处理文件 {i}/{len(input_files)}: {os.path.basename(input_file)}")
            
            try:
                reader = PcapReader(input_file)
                packet_count = 0
                
                # 批量处理数据包
                batch_size = 1000
                batch = []
                
                for packet in reader:
                    batch.append(packet)
                    packet_count += 1
                    
                    if len(batch) >= batch_size:
                        for p in batch:
                            writer.write(p)
                        batch = []
                        total_packets += len(batch) if batch else batch_size
                
                # 处理剩余的数据包
                for p in batch:
                    writer.write(p)
                total_packets += len(batch)
                
                reader.close()
                
                if verbose:
                    print(f"  -> 处理了 {packet_count} 个数据包")
                    
            except Exception as e:
                print(f"警告: 处理文件 {input_file} 时出错: {e}")
                continue
        
        writer.close()
        
        print(f"合并完成!")
        print(f"总共合并了 {total_packets} 个数据包")
        print(f"输出文件: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"错误: 合并过程中出现问题: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="合并抓包文件工具")
    parser.add_argument("-i", "--input-dir", default=".", 
                       help="输入目录 (默认: 当前目录)")
    parser.add_argument("-o", "--output", 
                       default=f"merged_pcap_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pcap",
                       help="输出文件名")
    parser.add_argument("-v", "--verbose", action="store_true",
                       help="详细输出")
    parser.add_argument("--memory-efficient", action="store_true",
                       help="使用内存高效模式 (适用于大文件)")
    parser.add_argument("--files", nargs="+",
                       help="指定要合并的文件列表")
    
    args = parser.parse_args()
    
    # 获取要合并的文件
    if args.files:
        input_files = args.files
    else:
        input_files = get_pcap_files(args.input_dir)
    
    if not input_files:
        print("错误: 没有找到抓包文件")
        return 1
    
    # 检查输出文件是否已存在
    if os.path.exists(args.output):
        response = input(f"输出文件 {args.output} 已存在，是否覆盖? (y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return 0
    
    # 选择合并方式
    if args.memory_efficient:
        success = merge_pcap_memory_efficient(input_files, args.output, args.verbose)
    else:
        success = merge_pcap_files(input_files, args.output, args.verbose)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
